<html>

<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<title>MonitoringTest</title>
	<script type="text/javascript" src="./js/spark-md5.min.js"></script>

	<style type="text/css">
		body {
			font-size: 13px;
			display: flex;
			align-items: flex-start;
			justify-content: center;
			height: calc(100% - 40px);
			overflow: hidden;
			padding: 10px 0;
		}

		body>div {
			height: 100%;
		}

		fieldset {
			margin: 0;
			padding: .5rem;
			border-radius: 3px;
		}

		body>div:nth-child(1) {
			width: 30%;
			max-width: 370px;
			overflow-y: auto;
			overflow-x: hidden;
		}

		ul {
			display: flex;
			padding: 0;
			margin: 0;
			flex-direction: column;
			justify-content: space-around;
		}

		ul>li {
			list-style: none;
			text-align: right;
			padding: 5px 0;
		}

		body>div.etc-center {
			height: 100%;
			margin-left: 1rem;
			width: calc(70% - 3px);
		}

		.etc-center>fieldset:nth-child(2) {
			height: calc(100% - 85px);
		}

		.etc-center>fieldset>.etc-message {
			height: 100%;
		}

		.etc-center>fieldset>textarea {
			height: 100%;
		}

		.etc-center>fieldset>.etc-message>textarea:nth-child(1) {
			margin-right: 16px;
		}

		.etc-message {
			display: grid;
			grid-template-columns: .5fr 1fr;
		}

		.etc-send {
			display: flex;
			flex-direction: column;
		}

		.etc-send>textarea {
			margin-right: 1rem;
		}

		.etc-send>textarea:nth-child(1) {
			margin-bottom: 1rem;
		}

		.etc-regx {
			display: none;
			text-align: left;
		}

		.etc-regx-ul {
			display: grid;
			justify-items: center;
			justify-content: space-between;
			grid-template-columns: max-content min-content;
		}

		.etc-regx-ul li {
			color: #999;
			font-size: .6rem;
		}

		.etc-codeList {
			color: #fff;
			background: #333;
			max-height: 300px;
			overflow: auto;
			padding: 3px 5px;
			border-radius: 3px;
		}

		.etc-codeList ul {
			padding-left: 1.5rem;
		}

		.etc-codeList li {
			text-align: left;
		}
	</style>
</head>

<body>
	<div class="etc-left">
		<fieldset>
			<legend>基本配置</legend>
			<ul>
				<li>
					<label>本机IP:</label><input type="text" id="localIp" value="***********">
				</li>
				<li>
					<label>WsUri:</label><input type="text" id="wsUri" value="ws://************:8000">
				</li>
				<li>
					<label>设备IP:</label><input type="text" id="devIp" value="***********">
				</li>
				<li>
					<label>操作ID:</label><input type="text" id="J8Id" value="">
				</li>
				<li>
					<label>To 设备类型:</label><input type="text" id="toDevType" value=''>
				</li>
				<li>
					<label>From 设备类型:</label><input type="text" id="fromDevType" value='J8_TYPE'>
				</li>
				<li>
					<label>拉流协议（0-U 1-M、2-T）:</label><input type="text" id="pullProtocol" value='1'>
				</li>
				<li>
					<label>收流端口:</label><input type="text" id="recvPort" value='6666'>
				</li>
				<li>
					<label>拉流源分组:</label>
					<select onchange="onSelectPullSrcChange();" id="sePullSrc">
						<option>empty</option>
						<option>pullsrc1</option>
						<option>pullsrc2</option>
						<option>pullsrc3</option>
						<option>pullsrc4</option>
						<option>pullsrc5</option>
					</select>
				</li>
			</ul>
		</fieldset>

		<fieldset>
			<legend>消息配置</legend>
			<ul>
				<li>
					<label>是否循环:</label><input type="checkbox" id="etEeach" />
				</li>
				<li>
					<label>是否替换:</label>
					<input type="checkbox" onclick="showRegxList(this)" id="etEreplace" />
				</li>
				<li>
					<label>每条消息生成一个Ws链接:</label><input type="checkbox" id="etEmultiSocket" />
				</li>
				<li>
					<label>消息执行次数:</label><input id="etEtimes" type="number" max="9999" min="1" value="1" />
				</li>
				<li>
					<label>消息间隔/毫秒:</label><input id="etEtimer" type="number" max="9999999" min="10" value="100"
						step="10" />
				</li>
				<li>
					<label>消息类型:</label>
					<select onchange="onSelectMsgChange();" id="seMsg">
						<option>----请选择消息----</option>
					</select>
				</li>

				<li id="etEregx" class="etc-regx">
					<fieldset>
						<legend>替换规则</legend>
						<ul class="etc-regx-ul">
							<li>当前日期:@{date}</li>
							<li>当前时间:@{time}</li>
							<li>唯一值:@{unique}</li>
							<li>发送索引值:@{index}</li>
							<li>随机数值:@{number}</li>
							<li>Base64:@{base64(b2311)}</li>
							<li>MD5加密:@{md5(1000101)}</li>
						</ul>
						<div class="etc-codeList">
							例:{<ul id="etECodeList"></ul>}
						</div>
					</fieldset>
				</li>
			</ul>
		</fieldset>
	</div>

	<div class="etc-center">
		<fieldset>
			<legend>操作控制</legend>
			<button onclick="initWebSocket()" id="etEonLine">连接服务器</button>
			<button onclick="stopWebSocket()" disabled id="etEofficeLine">断开连接</button>
			<button onclick="checkSocket()">状态</button>
			<button onclick="setMessage()" id="etEsendMsg">发送消息</button>
			<button onclick="resetSend()" id="etEresetMsg">停止发送</button>
			<button onclick="startPullStream()" id="etStartPullStream">开始拉流</button>
			<button onclick="stopPullStream()" id="etStopPullStream">停止拉流</button>
		</fieldset>
		<fieldset>
			<legend>消息内容</legend>
			<div class="etc-message">
				<div class="etc-send">
					<textarea id="inputTextArea" style="height: 100%;" placeholder="消息模板"
						onblur="setRegxArea(this.value,true)"></textarea>
					<textarea id="inputRegxArea" style="height: 100%;" placeholder="发送内容"
						onkeydown="if(event.keyCode==13)setMessage();" readonly></textarea>
				</div>
				<textarea id="debugTextArea" style="height:100%;"></textarea>
			</div>
			<textarea id="TisTextArea" style="width:1280px;height:720px;display: none;"></textarea>
		</fieldset>
	</div>
	<script src="./pullsrc1.js"></script>
	<script src="./pullsrc2.js"></script>
	<script src="./pullsrc3.js"></script>
	<script src="./pullsrc4.js"></script>
	<script src="./pullsrc5.js"></script>
	<script type="text/javascript">

		var _pullsrc = {
			pullsrc1: _pullsrc1,
			pullsrc2: _pullsrc2,
			pullsrc3: _pullsrc3,
			pullsrc4: _pullsrc4,
			pullsrc5: _pullsrc5,
		}
		var _index = 1, _timer = null,
			_config = { each: null, replace: null, times: 1, timer: 10, info: '', multi: false },
			_tis = '';
		/*` 
=========设备类型==========
  NONE_TYPE           = 0;      //空
ICCS_TYPE           = 1;      //ICCS
ICMS_TYPE           = 2;      //ICMS
OMSVR_TYPE          = 3;      //运维服务器
DEB_TYPE            = 4;      //测试用
ALMN_TYPE           = 5;      //ALM-N板
SIPS_TYPE           = 6;      //SIP-S板
SIPC_TYPE           = 7;      //SIP-C板
MAX_TYPE            = 8;      //最大值

STA_TYPE = 1,               // 模拟号码
SIP_TYPE = 2,               // SIP号码
XIXUN_TYPE = 3,             // 视讯号码
B5_TYPE = 4,                // 维护终端，1个 （扩容以后再说）   
B9_TYPE = 5,                // 总操作台，2个  （扩容以后再说） 
B8_TYPE = 6,                // OPR操作台，PC软终端
B310_TYPE = 7,              // B310单机，OPR用户，需注册固定的SIP-D
B300_TYPE = 8,              // B300单机，下级用户，需注册固定的SIP-D
COPT_TYPE = 9,              // 下级用户，PC软终端
DXU_TYPE = 10,              // 内部级联用户， 一对使用
IXU_MAS_TYPE = 11,          // 外部级联用户， 主模式
IXU_SLA_TYPE = 12,          // 外部级联用户， 从模式
RCU_TYPE = 13,              // 调度的录音端口
SPUSER_PC_TYPE = 14,        // 特殊用户 PC软终端
SPUSER_B300_TYPE = 15,      // 特殊用户 B300单机
SPDXU_MAS_TYPE = 16,        // 特殊用户的从属用户
SPDXU_SLA_TYPE = 17,        // 特殊用户的从属用户，加入调度群。
SHOUZHANG_TYPE = 18,        // 首长终端
ZUZHANG_TYPE = 19,          // 组长终端  
GANGWEI_TYPE = 20,          // 岗位终端
WEIHU_TYPE = 21,            // 维护终端  
CONF_DXU_TYPE = 22,         // 会商级联, 需要分配在SIP-D
CONF_RCU_TYPE = 23,         // 会商录音, 需要分配在SIP-D
CONF_IXU_MAS_TYPE = 24,     // 会商中继, 主模式，需要分配在SIP-D
CONF_IXU_SLA_TYPE = 25,     // 会商中继, 从模式，需要分配在SIP-D
IXU_SIP_MAX_TYPE = 26,      //IXU SIP中继主模式
IXU_SIP_SLA_TYPE = 27,      //IXU SIP中继从模式
`
*/
		var _socketList = [], _mulitTimes = 0, _multiSocketList = [],
			_list = ['toip', 'J8Id', 'devIp', 'localIp', 'remoteIp', 'toDevType', 'fromDevType', 'debugTextArea', 'inputTextArea', 'seMsg', 'sePullSrc'];

		for (var i = 0; i < _list.length; i++) {
			var v = _list[i];
			this[('_' + v)] = gById(v);
		}
		var _hb_timerid = undefined;
		var _hb_cnt = 0;
		var _seq = 1,
			_loginSta = 0,
			_websocket = null,
			_basePort = 8000,
			_wsUri = document.getElementById("wsUri").value,
			_chnlMsg = {
				msgtype: 3,
				msg: ''
			},
			_headTmp = {
				seqnum: 1,
				fromdev: _fromDevType.value,
				fromip: _localIp.value,
				fromport: 9000,
				todev: _toDevType.value,
				toip: _devIp.value,
				msgId: ''
			},
			_allMsg = {
				//登录请求，COM_LOGIN_REQ 0x0101
				regReq: {
					msgId: 'COM_LOGIN_REQ',
					Ac3sMsg: {
						head: _headTmp,
						regReq: {
							terreginfo: {
								account: 'cjf',
								passwd: '@{md5(cjf)}'
							}
						}
					}
				},
				regReqApp: {
					msgId: 'COM_LOGIN_REQ',
					Ac3sMsg: {
						head: _headTmp,
						regReq: {
							terreginfo: {
								account: 'admin',
								passwd: '21232f297a57a5a743894a0e4a801fc3',
								j8appinfo: {
									machine_id: 'JanffuChan',
									camera_account: 'admin',
									camera_passwd: '@{base64(admin)}',
									camera_port: 554
								}
							}
						}
					}
				},

				//登录回应，COM_LOGIN_RES 0x0102
				regRes: {
					msgId: 'COM_LOGIN_RES',
					Ac3sMsg: {
						head: _headTmp,
						regRes: {
							result: 'OK',
							datetime: ''
						}
					}
				},

				//iCMS发送初始数据给SIP-D(iCMS->SIP-D板) SIPD_INIT_DATA
				sipdinitdata: {
					msgId: 'SIPD_INIT_DATA',
					Ac3sMsg: {
						head: _headTmp,
						sipdinitdata: {
							opt: 'OPT_UPDATE',
							extnum: '1001',
							extType: 26,
							bindSipdIp: '',
							slaveInfo: {
								masterIp: '************',
								masterExt: '3007'
							},
							sipInfo: {
								localSipExt: '1001',
								remoteSipExt: '9003',
								remoteSipIp: '***********',
								remoteSipPort: 5080,
								isMaster: 1
							},
							codec: 'AUDIO_COMPRESS_G711'
						}
					}
				},

				////设置时隙信息(iCMS->SIP-D板)
				sipdsetsource: {
					msgId: 'SIPD_SET_SOURCE',
					Ac3sMsg: {
						head: _headTmp,
						sipdsetsource: {
							extnum: '2005',
							ctrl: 1,
							remoteip: '**************',
							remoteport: 8888,
							tone: 1
						}
					}
				},

				pullReq: {
					msgId: 'COM_PULL_STREAM_REQ',
					JxMsg: {
						head: _headTmp,
						pullReq: {
							start: true,
							protocol: 1,
							srcId: "12345678901320000100",
							recvIp: localIp.value,
							recvPort: 6032
						}

					}
				},
				comsvrtransstainforeq: {
					msgId: 'COM_SVR_TRANS_STA_INFO_REQ',
					JxMsg: {
						head: _headTmp,
						comsvrtransstainforeq: {
							opt: 'REQ_SVR_INFO',
							devid: ''
						}
					}
				},
				datareq: {
					msgId: 'DATA_OPERATION_REQ',
					JxMsg: {
						head: _headTmp,
						dataReq: {
							dbtype: "A_DB",
							method: "Get",
							get: {
								data: "{\"limit\":1000,\"offset\":0,\"table\":\"vid_codecchannel\"}"
							}
						}
					}
				}
			},
			_allMsgKeys = Object.keys(_allMsg);

		(gById("TisTextArea")).value = _tis;

		for (var i = 0; i < _allMsgKeys.length; i++) {
			var opt = document.createElement("option");
			opt.innerHTML = _allMsgKeys[i];
			_seMsg.appendChild(opt);
		}
		_allMsg['regRes']['Ac3sMsg']['regRes']['datetime'] = Date();
		//_inputTextArea.value = JSON.stringify(_allMsg['regRes'], ' ', 4);
		//_inputTextArea.scrollTop = _inputTextArea.scrollHeight;	

		function debug(message, scroll) {
			let timeStr = getTime(7);
			var d = new Date();
			timeStr = timeStr + "." + d.getMilliseconds();
			_debugTextArea.value += '\r\n------------- ' + timeStr + ': \n' + message + "\r\n";
			if (scroll !== undefined && scroll === true)
				_debugTextArea.scrollTop = _debugTextArea.scrollHeight;
		}

		function onSelectPullSrcChange() {
			var head = {
				seqnum: _seq++,
				fromdev: _fromDevType.value,
				fromip: _localIp.value,
				fromport: 9000,
				todev: _toDevType.value,
			};
		}

		function onSelectMsgChange() {
			var head = {
				seqnum: _seq++,
				fromdev: _fromDevType.value,
				fromip: _localIp.value,
				fromport: 9000,
				todev: _toDevType.value,
				//toip:_devIp.value
			};
			var msgKey = _seMsg.selectedOptions[0].innerHTML;
			let selectTmp = JSON.parse(JSON.stringify(_allMsg[msgKey]));
			let selectMsg = selectTmp['Ac3sMsg'];
			if (selectMsg === undefined)
				selectMsg = selectTmp['JxMsg'];
			let msgId = selectTmp['msgId'];
			if (selectMsg !== undefined) {
				selectMsg['head'] = head;
				selectMsg['head']['msgId'] = msgId;
				switch (msgKey) {
					case "regReq":
						//selectMsg['regReq']['vip']= _localIp.value;							
						break;
					case "regRes":
						selectMsg['regRes']['datetime'] = Date();
						break;
					case 'sipdsetsource':
						selectMsg['head']['todev'] = 'SIPD_TYPE';
						break;
				}
				console.log("select " + msgKey + ", msg:");
				console.log(JSON.stringify(selectMsg, ' ', 4));
				_inputTextArea.value = JSON.stringify(selectMsg, ' ', 4);
				_inputTextArea.scrollTop = _inputTextArea.scrollHeight;
			}
			setRegxArea(_inputTextArea.value || '', true)
		}

		function resetSend() {
			if (_timer) clearTimeout(_timer);
			_config = { each: null, replace: null, times: 1, timer: 10, info: '' };

			//_multiSocketList.length = 0;
			(gById('etEsendMsg')).removeAttribute('disabled');
			(gById('etEresetMsg')).setAttribute('disabled', true);
		}

		function setMessage() {
			var times = gById('etEtimes').value,
				timer = gById('etEtimer').value;

			(gById('etEresetMsg')).removeAttribute('disabled');
			(gById('etEsendMsg')).setAttribute('disabled', true);

			_config.times = (times < 2 ? 1 : times);
			_config.timer = (timer < 10 ? 10 : timer);
			_config.info = gById('inputTextArea').value;
			_config.each = gById('etEeach').checked || false;
			_config.replace = gById('etEreplace').checked || false;
			_config.multi = gById('etEmultiSocket').checked || false;

			_mulitTimes = times || 0;
			_multiSocketList.length = 0;
			if (times && _config.multi) _multiSocketList.length = times;

			for (let ix = 0; ix < times; ix++) {
				_multiSocketList[ix] = { "value": null, "index": 0 };
			}
			sendMessage()
		}

		function fillPullMsg(msgTmp, msg, msgHead, pullReq) {
			pullReq = {
				start: true,
				protocol: 1,
				srcId: "",
				recvIp: gById('localIp').value,
				recvPort: 6032
			};
			msgHead = {
				seqnum: _seq++,
				fromdev: "J8_TYPE",
				fromip: gById('localIp').value,
				toGbid: "FFFFFFFFFFFFFFFFFFFF",
				fromGbid: gById('J8Id').value,
				msgId: "COM_PULL_STREAM_REQ"
			};
			msg = {
				head: {},
				pullReq: {},
			}
			msgTmp = {
				msgtype: 4,
				msg: ""
			}

		}

		function sendMessage(newSocket) {
			var a3cMsg = JSON.parse(_config.info || '{}'),
				chnlMsg = JSON.parse(JSON.stringify(_chnlMsg || {}));
			if (typeof a3cMsg.head != 'object') a3cMsg['head'] = {};
			if ((a3cMsg.head['todev'] || '') === 'ICCS_TYPE') {
				a3cMsg.head['todev'] = undefined;
				a3cMsg.head['toip'] = undefined;
			}
			newSocket = newSocket || _websocket;
			let msgtype = chnlMsg.msgtype;
			if (a3cMsg.head['msgId'] === 'COM_PULL_STREAM_REQ')
				msgtype = 4;
			chnlMsg['msg'] = JSON.stringify(a3cMsg || {});

			if (newSocket != null) {
				var chnlObj = _chnlMsg, info = JSON.stringify(chnlMsg);
				chnlObj.msgtype = msgtype;
				if (_config.replace) {
					chnlObj.msg = JSON.stringify(JSON.parse(setRegxArea(_config.info)));
					info = JSON.stringify(chnlObj);
				}
				if (chnlMsg.hasOwnProperty('msgtype')) _index += 1;
				newSocket.send(info);
				debug('\n\nSend Msg: ' + info);

				if (_config.each) _timer = setTimeout(function () {//循环发送	
					if (_config.times < 1) {
						resetSend();
						debug('消息发送完毕');
					}

					const socket = _config.multi ? new WebSocket(_wsUri) : newSocket;

					if (_config.multi) {
						console.log('当前打开的Socket下标:', _config.times);
						_multiSocketList[_config.times] = { "value": socket, "index": 0 };
						_config.times -= 1;
					}
					else {
						if (_config.times > 0 && _timer) {
							_timer = null;
							sendMessage(socket);
						}
					}

					socket.onopen = function () {
						if (_config.times < 0 || !_timer) return;
						_timer = null;
						// _socketList.push(socket);
						//_multiSocketList[_config.times] = { "value": socket, "index": 0 };
						sendMessage(socket);
					}
					socket.onmessage = function (evt) {
						var data = JSON.parse(evt.data);
						if (data["api"] == "/heartbeat") {
							data["info"] = "pong";
							socket.send(JSON.stringify(data));
							return;
						}
						data.msg = JSON.parse(data.msg || '{}');
						debug("Message recv :" + JSON.stringify(data, ' ', 4));
						if (data.msg.head.msgId == 'COM_LOGIN_REQ') {
							let retRes = _allMsg['regRes']['Ac3sMsg'];
							retRes['head']['msgId'] = 'COM_LOGIN_RES';
							retRes['regRes']['datetime'] = Date();
							let wsMsg = {
								msgtype: 3,
								msg: JSON.stringify(retRes)
							};
							socket.send(JSON.stringify(wsMsg));
							let logStr = {
								msgtype: 3,
								msg: retRes
							}
							// console.log("Message send: ", '"' + JSON.stringify(logStr) + '"');
							debug("Message send: " + '"' + JSON.stringify(logStr, ' ', 4) + '"');
						}
					}
				}, _config.timer < 10 ? 10 : _config.timer);
				if (!(_config.each)) resetSend();
				return;
			}
			debug('服务器未链接\n')
		}

		function heartBeatAction() {
			_hb_timerid = setInterval(function () {
				let ix = 0; for (var item of _multiSocketList.reverse()) {
					item = item || { value: null, index: 0 };
					ix++; item.index += 1;
					if (!item.value || typeof item.value != 'object' || (item.value || {}).readyState != 1) {
						if ((item.index % 16) == 0) console.log('当前Socket下标未链接:', ix, '=', item);
						continue;
					}

					if ((item.index % 10) == 0) {
						debug("heart_beat:{method:'GET',api:'/heartbeat'}, counter:" + item.index);
						console.log('当前Socket下标及心跳下标:', ix, '=', item.index, '=>/heartbeat');
					}
					item.value.send('{"method": "GET","api": "/heartbeat"}');
				}
			}, 1000)
		}

		function showRegxList(o) {
			setRegxArea(((gById('inputTextArea')).value) || '', o.checked);
			(gById('etEregx')).style.display = o.checked ? 'block' : 'none';
			(gById('etECodeList')).innerHTML = resCodeFormat({
				"regReq": { "terreginfo": { "time": "@{time}", "account": "admin_@{index}", "passwd": "@{md5(123456)}" } }
			});
		}

		function initWebSocket() {
			_loginSta = 0;
			_onLine = gById('etEonLine');
			_wsUri = gById("wsUri").value;
			_offLine = gById('etEofficeLine');

			try {
				if (typeof MozWebSocket == 'function')
					WebSocket = MozWebSocket;
				if (_websocket && _websocket.readyState == 1)
					_websocket.close();

				_websocket = new WebSocket(_wsUri);

				_websocket.onopen = function (evt) {
					_onLine.setAttribute('disabled', true);
					_offLine.removeAttribute('disabled');
					// _socketList.push(_websocket);
					debug(_wsUri + ' 服务链接打开');
					if (_hb_timerid) clearInterval(_hb_timerid);

					heartBeatAction();
					/*
					_hb_timerid = setInterval(function () {
						let heartbeat = {
							method: "GET",
							api: "/heartbeat"
						};
						_hb_cnt++;
						let hb_str = JSON.stringify(heartbeat);
						if ((_hb_cnt % 10) == 0) {
							debug("heart_beat:" + hb_str + ", counter:" + _hb_cnt), false;
						}
						_websocket.send(hb_str);
					}, 1000);
					*/
				};
				_websocket.onclose = function (evt) {
					resetSend();
					debug(_wsUri + ' 服务链接关闭');
					/*
					if (_hb_timerid !== undefined) {
						clearInterval(_hb_timerid);
					}
					_hb_timerid = undefined;
					_hb_cnt = 0;
					*/
					_onLine.removeAttribute('disabled');
					_offLine.setAttribute('disabled', true);
				};
				_websocket.onmessage = function (evt) {
					var data = JSON.parse(evt.data || '{}');
					if (data["api"] == "/heartbeat") {
						data["info"] = "pong";
						_websocket.send(JSON.stringify(data));
					} else {
						var tmp = data;
						tmp.msg = JSON.parse(tmp.msg);
						debug("Message recv :" + JSON.stringify(tmp, ' ', 4));
						if (tmp.msg.head.msgId == 'COM_LOGIN_RES') {
							gById("J8Id").value = tmp.msg.head.toGbid;
						}
						if (tmp.msg.head.msgId == 'COM_LOGIN_REQ') {
							let retRes = _allMsg['regRes']['Ac3sMsg'];
							retRes['head']['msgId'] = 'COM_LOGIN_RES';
							retRes['regRes']['datetime'] = Date();
							let wsMsg = {
								msgtype: 3,
								msg: JSON.stringify(retRes)
							};
							_websocket.send(JSON.stringify(wsMsg));
							let logStr = {
								msgtype: 3,
								msg: retRes
							}
							console.log("Message send: ", '"' + JSON.stringify(logStr) + '"');
							debug("Message send: " + '"' + JSON.stringify(logStr, ' ', 4) + '"');
						}
					}
				};
				_websocket.onerror = function (evt) {
					debug('ERROR: ' + evt.data);
				};
			} catch (exception) {
				debug('ERROR: ' + exception);
			}
		}

		function stopWebSocket() {
			// for (var i = 0; i < _socketList.length; i++)
			// 	_socketList[i].close();
			// _loginSta = 0;
			// _socketList.length = 0;
			var lr = _multiSocketList.reverse();
			if (_hb_timerid) clearInterval(_hb_timerid);
			if (typeof _websocket == 'object') { _websocket.close(); _websocket = null }
			for (var i = 0; i < _multiSocketList.length; i++) {
				var lo = _multiSocketList[i] || { value: null, index: 0 };
				if (!lo.value || typeof lo.value != 'object') continue;
				(lo.value).close(); lo.value = null; lo.index = 0
			}
			_loginSta = 0;
			_multiSocketList.length = 0;
		}

		function gById(v) {
			return document.getElementById(v);
		}

		function checkSocket() {
			if (_websocket != null) {
				var stateStr;
				switch (_websocket.readyState) {
					case 0: {
						stateStr = "CONNECTING";
						break;
					}
					case 1: {
						stateStr = "OPEN";
						break;
					}
					case 2: {
						stateStr = "CLOSING";
						break;
					}
					case 3: {
						stateStr = "CLOSED";
						break;
					}
					default: {
						stateStr = "UNKNOW";
						break;
					}
				}
				debug("WebSocket state = " + _websocket.readyState + " ( " + stateStr + " )");
				if (_loginSta === 0)
					debug("登录状态:未登录/登录失败");
				else
					debug("登录状态:登录成功");
			} else {
				debug("WebSocket is null");
			}
		}

		function setRegxArea(v, l) {
			l = typeof l != 'boolean' ? _config.replace : l;
			v = l ? (changeRegx(v) || '') : v;
			(gById('inputRegxArea')).value = v;
			return v;
		}

		function changeRegx(res) {
			var regx = new RegExp(/(?<=\@\{).*?(?=\})/g),
				list = res.match(regx) || [], row = {
					'date': function () { return getTime(1) },
					'time': function () { return getTime(3) },
					'unique': function () { return uuidStr(8) },
					'number': function () { return (Math.floor(Math.random() * 99999999 - 1) + 1) },
					'index': function () { return _index || 1 },
					'md5': function (v) { return codeStrMD5(v) },
					'base64': function (v) { return window.btoa ? window.btoa(v) : btoa(v) },
				};
			for (var i = 0; i < list.length; i++) {
				var v = list[i], l = '', t = v, s = ((row[v] || function () { })()) || '';
				(['md5(', 'base64(']).some(function (_v, _i) {
					bl = v.indexOf(_v) == 0;
					if (!bl) return false;
					t = _v.substr(0, _v.indexOf('('));
					l = v.substr(0, v.lastIndexOf(')')).replace(_v, '');
					s = ((row[t] || function () { })(l || '')) || '';
					return bl;
				});
				res = res.replaceAll('@{' + v + '}', s);
			}
			return res;
		}

		function codeStrMD5(v) {
			var code = new SparkMD5(); code.append(v); return code.end();
		}

		function getTime(type) {
			//{9}星期
			var d = new Date(), r = {
				1: '${1}-${2}-${3}',
				2: '${1}年${2}月${3}号',
				3: '${1}-${2}-${3} ${4}:${5}:${6}',
				4: '${1}年${2}月${3}号 ${4}:${5}:${6}',
				5: '${1}年${2}月${3}号 星期${7}',
				6: '${1}${2}${3}${4}${5}${6}',
				7: '${1}年${2}月${3}号 星期${7} ${4}:${5}:${6}',
				8: '${3} ${4}:${5}:${6}',
				9: d.getTime(),
			};

			return (type === 9) ? r[type] : formatStr(
				[r[type || 3],
				d.getFullYear(),
				(d.getMonth() + 1),
				d.getDate(), d.getHours(),
				d.getMinutes(), d.getSeconds(),
				(('天一二三四五六')[(d.getDay() || 0)])],
				2
			)
		}

		function formatStr(obj, len) {
			if (!(obj instanceof Array) || obj.length < 2) return;
			var r, v = obj[0] ?? '';
			len = len || 0;
			for (var _i = 0; _i < obj.length; _i++) {
				if (_i < 1) continue;
				_v = obj[_i];
				_v = typeof _v == 'string' || typeof _v == 'number' ? _v : '';
				_v = (len > 0 && (_v.toString()).length < len) ? processNumber(_v, len) : _v;//填充
				v = v.replaceAll('${' + _i + '}', _v);
			}
			return v;
		}

		function uuidStr(l, d) {
			var r = '0123456789asdfghjklzxcvbnmqwertyuiopQWERTYUIOPASDFGHJKLZXCVBNM', uuid = [], i = 0;
			d = d || r.length;
			if (l) {
				for (i = 0; i < l; i++)uuid[i] = r[0 | Math.random() * d];
			} else {
				let a; uuid[14] = '4';
				uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
				for (i = 0; i < 36; i++) {
					if (!uuid[i]) {
						a = 0 | Math.random() * 16;
						uuid[i] = r[i == 19 ? (a & 0x3) | 0x8 : a]
					}
				}
			}
			return uuid.join('')
		}

		function resCodeFormat(o) {
			var li = [];
			for (var i in o) {
				var v = o[i] || {};
				if (v instanceof Object) { li.push('<li>"' + i + '-1":{<ul>' + resCodeFormat(v || {}) + '},</ul></li>'); continue; }
				li.push('<li>"' + i + '":' + (typeof v != Object ? '"' + v + '"' : (JSON.stringify(v))) + ',</li>')
			}
			return li.join('');
		}

		function processNumber(value, digits = 1, fill = 0, slice = false) {//数值 需填充的值长度 填充内容:为布尔值true时剪切长度 剪切长度 true
			if (['number', 'string'].includes(typeof value) && !isNaN(value) && value !== '') {
				var ar = [], rr = [], num = Number(value).toString(), len = digits - num.length;
				slice = fill === true ? true : slice;
				fill = ['number', 'string'].includes(typeof fill) ? fill : 0;
				for (var i = 0; i < len; i++) {
					ar.push(fill);
				}
				rr = num.split('') || [];
				for (var i = 0; i < rr.length; i++) {
					ar.push(rr[i]);
				}
				num = ar.join('');
				return !slice ? num : num.substring(0, digits);
			}
			return ''
		}

		function startPullStream() {
			if (gById('J8Id').value === "") {
				debug('\n!!!!!!!!请先登录');
				return;
			}
			var pullsrcKey = sePullSrc.selectedOptions[0].innerHTML;
			if (_pullsrc[pullsrcKey] === undefined) {
				debug('\n请选择要拉流源的组');
				return;
			}
			var srcs = _pullsrc[pullsrcKey];
			var msgTmp = {}, msg = {}, msgHead = {}, pullReq = {};
			pullReq = {
				start: true,
				protocol: gById('pullProtocol').value,
				srcId: "",
				recvIp: gById('localIp').value,
				recvPort: gById('recvPort').value
			};
			msgHead = {
				seqnum: _seq++,
				fromdev: "J8_TYPE",
				fromip: gById('localIp').value,
				toGbid: "FFFFFFFFFFFFFFFFFFFF",
				fromGbid: gById('J8Id').value,
				msgId: "COM_PULL_STREAM_REQ"
			};
			msg = {
				head: {},
				pullReq: {},
			}
			msgTmp = {
				msgtype: 4,
				msg: ""
			}
			var sendMsg = [];
			for (let i = 0; i < srcs.length; i++) {
				let srcId = srcs[i];
				if (srcId.length != 20) {
					debug('\n流ID长度不对: ' + srcId);
					continue;
				}
				let chnlType = srcId.substring(10, 13);
				if (chnlType !== "132" && chnlType !== "113") {
					debug('\n流ID类型不对（类型应为“132”或“113”）: ' + srcId);
					continue;
				}
				pullReq.start = true;
				pullReq.srcId = srcId;
				//if(srcId === '68050000001130000000')
				//pullReq.protocol = 0;
				msgHead.seqnum = _seq++;
				msg.head = msgHead;
				msg.pullReq = pullReq;
				msgTmp.msg = JSON.stringify(msg);
				sendMsg.push(JSON.stringify(msgTmp));
			}
			var sendIdx = 0;
			var sendTotal = sendMsg.length;
			var gap = gById("etEtimer").value;
			const intervalId = setInterval(function () {
				if (sendIdx < sendTotal) {
					var sendStr = sendMsg[sendIdx];
					sendIdx++;
					_websocket.send(sendStr);
					debug('发送拉流请求消息: ' + sendStr);
				}
				else {
					clearInterval(intervalId);
					debug('拉流请求消息发送完成，发送数量为: ' + sendIdx);
				}
			}, gap);
		}

		function stopPullStream() {
			if (gById('J8Id').value === "") {
				debug('\n!!!!!!!!请先登录');
				return;
			}

			var pullsrcKey = sePullSrc.selectedOptions[0].innerHTML;
			if (_pullsrc[pullsrcKey] === undefined) {
				debug('\n!!!!!!!!请选择要取消拉流源的组');
				return;
			}
			var srcs = _pullsrc[pullsrcKey];
			var msgTmp = {}, msg = {}, msgHead = {}, pullReq = {};
			pullReq = {
				start: false,
				protocol: 1,
				srcId: "",
				recvIp: gById('localIp').value,
				recvPort: 6032
			};
			msgHead = {
				seqnum: _seq++,
				fromdev: "J8_TYPE",
				fromip: gById('localIp').value,
				toGbid: "FFFFFFFFFFFFFFFFFFFF",
				fromGbid: gById('J8Id').value,
				msgId: "COM_PULL_STREAM_REQ"
			};
			msg = {
				head: {},
				pullReq: {},
			}
			msgTmp = {
				msgtype: 4,
				msg: ""
			}
			var sendMsg = [];
			for (let i = 0; i < srcs.length; i++) {
				let srcId = srcs[i];
				if (srcId.length != 20) {
					debug('\n流ID长度不对: ' + srcId);
					continue;
				}
				let chnlType = srcId.substring(10, 13);
				if (chnlType !== "132" && chnlType !== "113") {
					debug('\n流ID类型不对（类型应为“132”或“113”）: ' + srcId);
					continue;
				}
				pullReq.start = false;
				pullReq.srcId = srcId;
				msgHead.seqnum = _seq++;
				msg.head = msgHead;
				msg.pullReq = pullReq;
				msgTmp.msg = JSON.stringify(msg);
				sendMsg.push(JSON.stringify(msgTmp));
			}
			var sendIdx = 0;
			var sendTotal = sendMsg.length;
			var gap = gById("etEtimer").value;
			const intervalId = setInterval(function () {
				if (sendIdx < sendTotal) {
					var sendStr = sendMsg[sendIdx];
					sendIdx++;
					_websocket.send(sendStr);
					debug('发送拉流取消消息: ' + sendStr);
				}
				else {
					clearInterval(intervalId);
					debug('拉流取消消息发送完成，发送数量为: ' + sendIdx);
				}
			}, gap);
		}

	</script>
</body>

</html>