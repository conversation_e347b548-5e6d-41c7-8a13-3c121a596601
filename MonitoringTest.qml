import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

ApplicationWindow {
    id: window
    visible: true
    width: 1280
    height: 720
    title: qsTr("MonitoringTest")
    font.pixelSize: 13

    Rectangle {
        anchors.fill: parent
        color: "#ffffff"

        RowLayout {
            anchors.fill: parent
            anchors.margins: 10
            spacing: 12

            // Left column ~30%
            ScrollView {
                Layout.preferredWidth: Math.min(parent.width * 0.30, 370)
                Layout.fillHeight: true

                ColumnLayout {
                    width: parent.width
                    spacing: 12

                    GroupBox {
                        title: qsTr("基本配置")
                        Layout.fillWidth: true

                        ColumnLayout {
                            anchors.margins: 8
                            spacing: 8

                            RowLayout { spacing: 6; Label { text: "本机IP:" } ; TextField { id: localIp; text: "***********"; Layout.fillWidth: true } }
                            RowLayout { spacing: 6; Label { text: "WsUri:" } ; TextField { id: wsUri; text: "ws://************:8000"; Layout.fillWidth: true } }
                            RowLayout { spacing: 6; Label { text: "设备IP:" } ; TextField { id: devIp; text: "***********"; Layout.fillWidth: true } }
                            RowLayout { spacing: 6; Label { text: "操作ID:" } ; TextField { id: J8Id; text: ""; Layout.fillWidth: true } }
                            RowLayout { spacing: 6; Label { text: "To 设备类型:" } ; TextField { id: toDevType; text: ""; Layout.fillWidth: true } }
                            RowLayout { spacing: 6; Label { text: "From 设备类型:" } ; TextField { id: fromDevType; text: "J8_TYPE"; Layout.fillWidth: true } }
                            RowLayout { spacing: 6; Label { text: "拉流协议（0-U 1-M、2-T）:" } ; TextField { id: pullProtocol; text: "1"; Layout.fillWidth: true } }
                            RowLayout { spacing: 6; Label { text: "收流端口:" } ; TextField { id: recvPort; text: "6666"; Layout.fillWidth: true } }
                            RowLayout { spacing: 6; Label { text: "拉流源分组:" } ; ComboBox { id: sePullSrc; Layout.fillWidth: true; model: ["empty","pullsrc1","pullsrc2","pullsrc3","pullsrc4","pullsrc5"] } }
                        }
                    }

                    GroupBox {
                        title: qsTr("消息配置")
                        Layout.fillWidth: true

                        ColumnLayout {
                            anchors.margins: 8
                            spacing: 8

                            RowLayout { spacing: 6; Label { text: "是否循环:" } ; CheckBox { id: etEeach } }
                            RowLayout { spacing: 6; Label { text: "是否替换:" } ; CheckBox { id: etEreplace; onClicked: regxArea.visible = checked } }
                            RowLayout { spacing: 6; Label { text: "每条消息生成一个Ws链接:" } ; CheckBox { id: etEmultiSocket } }
                            RowLayout { spacing: 6; Label { text: "消息执行次数:" } ; SpinBox { id: etEtimes; from: 1; to: 9999; value: 1 } }
                            RowLayout { spacing: 6; Label { text: "消息间隔/毫秒:" } ; SpinBox { id: etEtimer; from: 10; to: 9999999; step: 10; value: 100 } }
                            RowLayout { spacing: 6; Label { text: "消息类型:" } ; ComboBox { id: seMsg; model: ["----请选择消息----"]; Layout.fillWidth: true } }

                            // 替换规则区域（默认隐藏）
                            Rectangle { id: regxArea; visible: false; color: "transparent"; Layout.fillWidth: true; radius: 3; border.width: 0
                                ColumnLayout { anchors.fill: parent; spacing: 6
                                    GroupBox { title: qsTr("替换规则"); Layout.fillWidth: true
                                        ColumnLayout { anchors.margins: 6; spacing: 6
                                            Text { text: "当前日期:@{date}  当前时间:@{time}  唯一值:@{unique}  发送索引值:@{index}  随机数值:@{number}"; wrapMode: Text.Wrap; font.pixelSize: 11 }
                                            Text { text: "Base64:@{base64(b2311)}  MD5:@{md5(1000101)}"; wrapMode: Text.Wrap; font.pixelSize: 11 }
                                            Rectangle { color: "#333"; radius: 3; Layout.fillWidth: true; height: 80; Text { id: etECodeList; anchors.fill: parent; color: "#fff"; text: "例: …"; wrapMode: Text.Wrap } }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Center / Right column ~70%
            ColumnLayout {
                Layout.fillWidth: true
                Layout.fillHeight: true
                spacing: 12
                anchors.margins: 0

                GroupBox {
                    title: qsTr("操作控制")
                    Layout.fillWidth: true

                    RowLayout { anchors.margins: 8; spacing: 8
                        Button { id: etEonLine; text: "连接服务器" }
                        Button { id: etEofficeLine; text: "断开连接"; enabled: false }
                        Button { text: "状态" }
                        Button { id: etEsendMsg; text: "发送消息" }
                        Button { id: etEresetMsg; text: "停止发送" }
                        Button { id: etStartPullStream; text: "开始拉流" }
                        Button { id: etStopPullStream; text: "停止拉流" }
                    }
                }

                GroupBox {
                    title: qsTr("消息内容")
                    Layout.fillWidth: true
                    Layout.fillHeight: true

                    RowLayout {
                        anchors.margins: 8; spacing: 12
                        // Left: send / generated
                        ColumnLayout { Layout.fillWidth: true; Layout.preferredWidth: parent.width * 0.45; spacing: 8
                            TextArea { id: inputTextArea; placeholderText: qsTr("消息模板"); wrapMode: TextEdit.Wrap; Layout.fillHeight: true } 
                            TextArea { id: inputRegxArea; placeholderText: qsTr("发送内容"); wrapMode: TextEdit.Wrap; readOnly: true; Layout.fillHeight: true }
                        }

                        // Right: debug
                        TextArea { id: debugTextArea; placeholderText: qsTr("调试信息"); wrapMode: TextEdit.Wrap; Layout.fillWidth: true; Layout.fillHeight: true }
                    }

                    // hidden large textarea like TisTextArea
                    TextArea { id: TisTextArea; visible: false; width: 1280; height: 720 }
                }
            }
        }
    }
}
