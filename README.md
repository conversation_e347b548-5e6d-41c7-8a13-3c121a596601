# MonitoringTest QML

此仓库包含将 `MonitoringTest.html` 转换为 QML 的示例 `MonitoringTest.qml`。目标是尽量保持原 HTML 的 UI 布局与控件。

如何运行

- 在已安装 Qt 的机器上，你可以使用 `qmlscene` 快速查看：

```powershell
qmlscene MonitoringTest.qml
```

- 或在 Qt Creator 中打开该 `.qml` 文件并运行。

注意

- QML 中保留了控件 id（例如 `localIp`, `wsUri`, `J8Id`, `sePullSrc`, `inputTextArea`, `debugTextArea` 等），你可以在 QML/Qt 侧添加对应的逻辑或通过 C++/Python 与之交互。
- 该 QML 为视觉和布局的初步移植，部分 JavaScript 逻辑（如 WebSocket、替换规则、MD5）未自动迁移。可以根据需要将原 JS 的功能移植到 QML JS 或后端。
